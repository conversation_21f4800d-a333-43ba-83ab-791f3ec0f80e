from typing import Any

from langchain.output_parsers import OutputFixingParser
from langchain_core.output_parsers import SimpleJsonOutputParser
from langchain_core.prompts import ChatPromptTemplate

from src.helper.client.model import get_chat_model


def create_document_summary(title: str, context: str) -> tuple[Any, str | Any]:
    system_prompt = '''
===== 角色 =====
* 作为文档摘要编写专家，请根据以下文档内容生成简洁而有效的摘要,
摘要应涵盖每个主要部分的核心要点，并明确文档的目的、作用，以及相关的关键术语，以促进摘要存储在向量数据库中被有效检索。

===== 文档名称 =====    
{title}
      
===== 文档内容 =====
{context}
      
===== 输出格式 =====      
{{
    "summary": "str,摘要内容",
    "keywords": "str,从文档内容中提取关键词，格式：关键词 关键词 关键词 ..."
}}

===== 摘要(summary)要求 =====   
1.提炼了文档的主要思想和意图（提供摘要生成规范，优化存储和检索）。
2.概括了每个主要部分的关键信息。
3.突出了关键术语，增强搜索可见性。
4.使用简单语言，避免冗余（例如，不描述具体技术细节）。
5.确保所有术语的使用正确且一致。

===== 关键字(keywords)要求 =====
关键字仅能包含以下几种类型：
1.专业术语：文档中出现的技术概念、专业名词
2.产品/系统名称：具体的产品、xxx平台、xxx工具、xxx系统名称
3.核心功能：文档重点讨论的主要xx功能或xx主题
4.操作对象：文档涉及的具体操作xx目标或处理xx对象
5.文档标题：从文档标题中提取
6.关键字中不应包含动词，必须是为名词，不能是数字
7.不能单独使用例如：升级 场景 必须包含主语 xxx升级、xxx场景、xxx指南、xxx方案、xxx预案、xxx流程 禁止单独一个名字,必须是复合词

===== 开始编写 =====
* 遵循<编写要求>编写，最后严格按着<输出格式>进行结果的输出
    '''
    prompt = ChatPromptTemplate.from_messages(
        [
            ("system", system_prompt),
        ]
    )
    llm = get_chat_model(model_name="summary")
    chain = prompt | llm

    output = chain.invoke(
        {
            "title": title,
            "context": context,
        }
    )

    parser = OutputFixingParser(retry_chain=llm, parser=SimpleJsonOutputParser())

    rnt = parser.parse(output.content)

    print(rnt['keywords'])

    return rnt['keywords'], (title + "\n" + rnt['summary'])
