from typing import Dict, Any

from langchain.output_parsers import OutputFixingParser
from langchain_core.language_models import BaseLanguageModel
from langchain_core.output_parsers import SimpleJsonOutputParser
from langchain_core.prompts import PromptTemplate

from src.helper.client.model import get_chat_model

intent_analysis_template = """
功能要求：
根据用户的问题，分析用户的查询意图，提取用户想要查询的目标对象和相关关键词。

分析要求：
1. 准确识别用户想要查询的核心对象或主题
2. 提取与查询相关的关键词，用空格分隔
3. 关键词应该包含用户问题中的重要名词、动词和形容词
4. 去除无意义的停用词（如：的、了、吗、呢、怎么、如何等）
5. 保持关键词的原始形式，不要过度拆分

输出格式：
请严格按照以下 JSON 格式返回分析结果：
{{
    "intent": "提取用户想要了解或操作的对象，例如：日志规范是什么，提取：日志规范，如何申请主机权限，提取：申请主机权限。",
    "keywords": "关键词1 关键词2 关键词3， 从意图中提取操作对象和要进行的操作组合，最少五个，例如：日志规范、权限申请、主机权限、申请权限这种复合关键词"
}}

示例：
用户问题：我们有哪些数据库预案
分析结果：
{{
    "intent": "数据库预案",
    "keywords": "数据库 数据库预案"
}}

用户问题：如何配置Redis集群
分析结果：
{{
    "intent": "Redis集群配置",
    "keywords": "配置Redis Redis集群 配置集群"
}}

用户问题：服务器监控告警规则怎么设置
分析结果：
{{
    "intent": "服务器监控告警规则设置",
    "keywords": "服务器 监控告警 告警规则 规则设置"
}}

用户问题：
{question}
"""

INTENT_ANALYSIS_PROMPT = PromptTemplate(
    template=intent_analysis_template, input_variables=["question"]
)


class IntentAnalysisChain:
    """意图分析链，用于拆分用户问题的查询意图"""

    def __init__(
            self,
            llm: BaseLanguageModel,
            prompt: PromptTemplate = INTENT_ANALYSIS_PROMPT,
    ):
        self.llm = llm
        self.prompt = prompt
        self.parser = OutputFixingParser(retry_chain=llm, parser=SimpleJsonOutputParser())

    def invoke(self, input_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        执行意图分析的主要逻辑

        Args:
            input_data: 包含 "question" 字段的输入数据
            config: 配置信息，包含回调等

        Returns:
            包含 target 和 keywords 的字典
            格式：{"target": "查询对象", "keywords": "关键词1 关键词2"}
        """
        question = input_data["question"]

        # 1. 使用提示模板格式化
        formatted_prompt = self.prompt.format(question=question)

        # 2. 调用 LLM
        llm_response = self.llm.invoke(formatted_prompt)

        # 3. 解析输出
        parsed_result = self.parser.parse(llm_response.content)

        return parsed_result


def user_question_intent_analysis(question: str, prompt: PromptTemplate = INTENT_ANALYSIS_PROMPT) -> tuple[str, str]:
    llm = get_chat_model(model_name="summary")
    chain = IntentAnalysisChain(llm, prompt)

    rtn = chain.invoke(
        {"question": question},
    )
    if 'intent' in rtn and 'keywords' in rtn:
        if rtn['intent'] and rtn['keywords']:
            rtn['intent'] = rtn['intent'].strip()
            rtn['keywords'] = rtn['keywords'].strip()
            return rtn['intent'], rtn['keywords']
    return question, ""
