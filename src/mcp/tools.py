from typing import List

from langchain_core.tools import BaseToolkit, BaseTool

from settings import get_or_creat_settings_ins
from src.core.rag.doa import get_all_common_knowledge_base
from src.helper.client.model import get_chat_model
from src.mcp.mix_query import <PERSON><PERSON><PERSON><PERSON>Tool
from src.mcp.vectordb import QueryVectorDB
from src.modules.llms import LLMFactory, EmbeddingType


class VectorTookit(BaseToolkit):

    def get_tools(self) -> List[BaseTool]:
        config = get_or_creat_settings_ins()
        embedding = LLMFactory(config.embedding).BuildEmbedding(EmbeddingType.HUGGINGFACE)

        dbs = get_all_common_knowledge_base()

        # 按 query_index 去重
        unique_by_query_index = {}
        for item in dbs:
            if item.query_index not in unique_by_query_index:
                unique_by_query_index[item.query_index] = item

        # 去重后的结果列表
        deduplicated_list = list(unique_by_query_index.values())

        tools = [MixQueryTool(collection_name=deduplicated_list[0].query_index)]

        for item in deduplicated_list:
            tools.append(
                QueryVectorDB(
                    name=item.query_index,
                    doc_llm=get_chat_model(),
                    em=embedding,
                    description=item.description,
                    query_index=item.query_index,
                )
            )
        return tools
