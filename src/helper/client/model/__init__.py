import os
from functools import cache

from langchain.chat_models import init_chat_model
from langchain_community.chat_models import Cha<PERSON><PERSON><PERSON><PERSON>
from langchain_core.language_models import BaseChatModel
from langchain_openai.chat_models import AzureChatOpenAI

from settings import get_or_creat_settings_ins
from src.helper.custom_chat_models import VllmQwen3ChatModel


@cache
def get_chat_model(model_name: str = "default") -> BaseChatModel:
    config = get_or_creat_settings_ins()
    models = config.models
    model_cfg = getattr(models, "default")
    if model_name in models.model_fields_set:
        model_cfg = getattr(models, model_name)
    llm = config.llms[model_cfg]

    if llm.provider == "qwen":
        return ChatTongyi(
            top_p=llm.temperature,
            model=llm.model,
            **(llm.external_args or {}),
        )
    elif llm.provider == "vllm_qwen3":
        return VllmQwen3ChatModel(
            model=llm.model,
            temperature=llm.temperature,
            extra_body=llm.extra_body,
            **(llm.external_args or {}),
        )
    elif llm.provider == "azure_openai":
        # 直接使用AzureChatOpenAI以支持每个模型独立的API key配置
        external_args = llm.external_args or {}
        if llm.model == "gpt-4o":
            external_args['api_key'] = os.environ.get('AZURE_OPENAI_GPT_4O_API_KEY')
            return AzureChatOpenAI(
                model=llm.model,
                temperature=llm.temperature,
                max_tokens=llm.max_tokens,
                **external_args,
            )
        elif llm.model == "o1":
            external_args['api_key'] = os.environ.get('AZURE_OPENAI_API_KEY')
            return AzureChatOpenAI(
                model=llm.model,
                **external_args,
            )
    return init_chat_model(
        llm.model,
        model_provider=llm.provider,
        **(llm.external_args or {}),
    )
