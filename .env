LARK__APP_ID=********************
LARK__APP_SECRET=TEQqjQjYaRqGK4IFc2ADhfYNVhxs58gY
LARK__ENCRYPT_KEY=iTMpQufMlnGeFOjZCCnvwdFZAnPBVXPR
LARK__VERIFIVATION_TOKEN=iTMpQufMlnGeFOjZCCnvwdFZAnPBVXPR

AZURE_OPENAI_API_KEY=19e52cd198c5415d8752be0b5d60791d

AZURE_OPENAI_GPT_4O_API_KEY=67c92ace109b428c91562957bf9c76fc

AWS_ACCESS_KEY_ID=********************
AWS_SECRET_ACCESS_KEY=9AwvrXZdGKrBuOYU+N85oY//a4bap/IXBn8J7cSu

DASHSCOPE_API_KEY=sk-64a77f5b511144e183260e23db9b1197


#DEEPSEEK_API_KEY=UU40UDEbBR-lllwDoEmnukWUnkOyJIbrCa0RJIknPdnyiEL8oYx2-nqaziqEd6LBTdKle3kK6aT_hPUxJaLh7Q

DEEPSEEK_API_KEY=sk-GpbnzH4ZMuhNo3wrgS6TzZ3vUFloPFh7RT6HWBuzqRGfNgVZ
# DEEPSEEK_API_KEY=***********************************


POSTGRES__PASSWORD=5t8KV46htHrpGrQUASV8
MILVUS__PASSWORD=Jb3c6eLigbqOIm


LANGFUSE_PUBLIC_KEY=pk-lf-5edca58b-288e-4955-9daa-7f49cb3a8f24
LANGFUSE_SECRET_KEY=******************************************
LANGFUSE_HOST=http://************:8050

KNOWLEDGE_ENV=test