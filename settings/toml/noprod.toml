[lark]
name = "知了测试版"

[postgres]
user = "checkpoint_rw"
host = "***********"
port = 5432
database = "langgraph_checkpoint"
min_size = 1
max_size = 10

[milvus]
uri = "https://in01-55c8dcbceba5eb8.tc-ap-beijing.vectordb.zilliz.com.cn:443"
user = "yw_ops"
db_name = "yw_ops_db"
timeout = 20

[embedding]
model = "http://************:8081"

[rerank]
url = "http://************:8083"

[knowledge_base]
index = "rag_common_noprod"
ignore_docs = "月会|周会|会议纪要|OKR|故障报告|复盘|演练|用户调研|访谈|培训|调研报告|调研结果|体验报告|改造|变更分析|变更记录|故障总结|故障数据|版本发布"
use_cache = true

[models]
default = "gpt4o"
summary= "local-qwen3-thinking"
doc_llm = "o1"

[llms.gpt4o]
provider = "azure_openai"
temperature = 0
model = "gpt-4o"
external_args = { azure_endpoint = "https://east-us-quwan-yw-infra-01.openai.azure.com", azure_deployment = "gpt-4o", api_version = "2024-02-15-preview" }

[llms.o1]
provider = "azure_openai"
temperature = 0
model = "o1"
external_args = { azure_endpoint = "https://east-us2-quwan-yw-infra-02.openai.azure.com", azure_deployment = "o1", api_version = "2025-01-01-preview" }

[llms.qwen-max]
temperature = 0.01
model = "qwen-max-2025-01-25"
provider = "qwen"

[llms.local-qwq]
temperature = 0
model = "Qwen/QwQ-32B"
provider = "deepseek"
max_tokens = 8192
external_args = { api_base = "http://10.65.230.19:8001/v1", api_key = "quwan" }

[llms.local-qwen3-thinking]
temperature = 0.6
model = "Qwen/Qwen3-32B"
provider = "vllm_qwen3"
max_tokens = 10240
external_args = { base_url = "http://10.65.230.19:8004/v1", api_key = "quwan" }
extra_body = { "chat_template_kwargs" = { "enable_thinking" = true } }
